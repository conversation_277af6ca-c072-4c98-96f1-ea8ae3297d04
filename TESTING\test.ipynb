import pandas as pd
import matplotlib.pyplot as plt

# Load data, skipping metadata rows (adjust the number if needed)
df = pd.read_csv("A1 (1).csv", encoding='cp1252', skiprows=31)

# Plot all absorbance vs wavelength curves
plt.figure(figsize=(10, 6))
wavelengths = df.iloc[:, 0]  # First column is Wavelength

for col in df.columns[1:]:
    plt.plot(wavelengths, df[col], label=col)

plt.xlabel("Wavelength (nm)")
plt.ylabel("Absorbance")
plt.ylim(0, 3)
plt.xlim(400, 1000)
plt.title("Absorbance vs Wavelength at Different Timestamps")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()


import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from scipy.special import expit
from sklearn.metrics import r2_score


# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A1 (1).csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 181, 15), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 181, 15))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()


def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 181, 15))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)

def gualtieri_model(t, kg, a, b):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation

alpha_data = A_t_450
t_data = np.array(range(0, 181, 15))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 50, 5)
b_values = np.linspace(1, 50, 5)

best_error = np.inf
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g, a, b = popt
                gualtieri_array = gualtieri_model(t_data, K_g, a, b)
                error = np.linalg.norm(gualtieri_array - alpha_data)

                if error < best_error and a > 0 and b > 0 and K_g > 0:
                    best_error = error
                    best_params = (K_g, a, b)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue
            
if best_params is not None:
    K_g, a, b = best_params
    K_n = 1 / a
    error = np.linalg.norm(np.array(best_gualtieri_array) - alpha_data)

fit_quality = "Good" if error < 0.1 else "Poor"

plt.plot(t_data, best_gualtieri_array, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data, label='Data', marker='o', linestyle='--')
plt.text(170, 0.1, f'K_g = {K_g:.4f}\na = {a:.4f}\nb = {b:.4f}\nK_n = {K_n:.4f}\nErr = {error:.4f}\n{fit_quality}',
         fontsize=10, verticalalignment='bottom', horizontalalignment='center')

plt.xlabel("Time (min)")
plt.ylabel("Normalized Absorbance")
plt.grid(True)
plt.legend()
plt.show()

# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A1 (2).csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 241, 20), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 241, 20))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()

def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 241, 20))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)

# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A2.csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 221, 20), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 221, 20))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()

def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 221, 20))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)

# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A3.csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 221, 20), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 221, 20))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()

def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 221, 20))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)

# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A4.csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 221, 20), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 221, 20))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()

def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 221, 20))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)

# Load the CSV while skipping the metadata (approx 30 lines)
df = pd.read_csv("A5.csv", encoding='cp1252', skiprows=31)
# Make sure the first column is Wavelength and convert it to numeric
df.iloc[:, 0] = pd.to_numeric(df.iloc[:, 0], errors='coerce')

# Find the row where Wavelength is 450 nm
row_450nm = df[df.iloc[:, 0] == 450.4]

if not row_450nm.empty:
    absorbance_at_450nm = row_450nm.iloc[0, 1:]  # Skip the wavelength column
    # print("Absorbance values at 450 nm:")
    # print(absorbance_at_450nm)
else:
    print("450 nm wavelength not found in the data.")

I_t_450 = absorbance_at_450nm.values
A_t_450 = (I_t_450 - np.min(I_t_450)) / (np.max(I_t_450) - np.min(I_t_450))

plt.plot(range(0, 221, 20), A_t_450, marker='o')
plt.xlabel("Time (min)")
plt.xticks(ticks=range(0, 221, 20))
plt.ylabel("Normalized Absorbance")
plt.title("Normalized Absorbance vs Time")
plt.grid(True)
plt.show()

def gualtieri_model(t, kg, a, b, I_max):
        growth = 1 - np.exp(-(kg * t)**3)
        nucleation = 1 / (1 + np.exp(-(t - a) / b))
        return growth * nucleation * I_max


alpha_data = np.array(I_t_450) - min(I_t_450)
t_data = np.array(range(0, 221, 20))
Kg_values = np.linspace(0.01, 0.1, 100)
a_values = np.linspace(1, 5, 5)
b_values = np.linspace(1, 5, 5)
I_max = max(alpha_data)

best_r2 = 0
best_params = None
best_gualtieri_array = None

for Kg_init in Kg_values:
    for a_init in a_values:
        for b_init in b_values:
            try:
                p0 = [Kg_init, a_init, b_init, I_max]
                popt, pcov = curve_fit(gualtieri_model, t_data, alpha_data, p0=p0, maxfev=10000)
                K_g = popt[0]
                a = popt[1]
                b = popt[2]
                I_max = popt[3]
                
                gualtieri_array = [gualtieri_model(t, K_g, a, b, I_max) for t in t_data]
                r2 = r2_score(alpha_data, gualtieri_array)

                if r2 > best_r2 and a > 0 and b > 0 and K_g > 0:
                    best_r2 = r2
                    best_params = (K_g, a, b, I_max)
                    # print(best_params)
                    best_gualtieri_array = gualtieri_array

            except RuntimeError:
                continue


if best_params is not None:
    K_g, a, b, I_max = best_params
    K_n = 1 / a
    r2 = r2_score(alpha_data, best_gualtieri_array)
else:
    best_gualtieri_array = [0] * len(t_data)
    
fit_quality = "Good" if r2 > 0.9 else "Poor"

plt.plot(t_data, np.array(best_gualtieri_array) / I_max, label='Gualtieri Model', color='red')
plt.plot(t_data, alpha_data / I_max, label='Data', marker='o', linestyle='--')
plt.xlabel("Time (min)")
plt.ylabel("Extent of Reaction")
plt.grid(True)
plt.legend()

# Annotate fit parameters 
plt.text(170, 0.1, f'K_g={K_g:.4f}\na={a:.4f}\nb={b:.4f}\nK_n={K_n:.4f}\nR²={r2:.4f}\n{fit_quality}', fontsize=9,
        ha='center', va='bottom', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.6))
# print(best_params)